import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("Installing required packages...")
    
    try:
        # Install pip packages
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ All packages installed successfully!")
        
    except subprocess.CalledProcessError as e:
        print(f"Error installing packages: {e}")
        return False
    
    return True

def check_microphone():
    """Check microphone availability"""
    try:
        import speech_recognition as sr

        # Initialize recognizer and microphone
        recognizer = sr.Recognizer()
        microphone = sr.Microphone()

        print("\nChecking microphone...")
        print("-" * 30)

        # List available microphones
        mic_list = sr.Microphone.list_microphone_names()
        print(f"Available microphones: {len(mic_list)}")
        for i, name in enumerate(mic_list):
            print(f"  {i}: {name}")

        # Test default microphone
        print(f"\nDefault microphone: {mic_list[0] if mic_list else 'None'}")

        # Test microphone access
        with microphone as source:
            print("Testing microphone access...")
            recognizer.adjust_for_ambient_noise(source, duration=1)
            print("✓ Microphone access successful!")

    except ImportError:
        print("SpeechRecognition not installed yet")
    except Exception as e:
        print(f"Error checking microphone: {e}")

def main():
    print("System Audio AI Setup")
    print("=" * 30)
    
    # Install requirements
    if install_requirements():
        print("\nSetup completed successfully!")

        # Check microphone
        check_microphone()
        
        print("\nTo run the System Audio AI:")
        print("python system_audio_ai.py")
        
    else:
        print("Setup failed. Please check the error messages above.")

if __name__ == "__main__":
    main()
