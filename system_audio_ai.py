import speech_recognition as sr
import google.generativeai as genai
import threading
import time
import os
import subprocess
import tempfile
from datetime import datetime

class SystemAudioAI:
    def __init__(self, api_key):
        # Configure Gemini AI
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-1.5-flash')

        # Audio settings
        self.RECORD_SECONDS = 5  # Record in 5-second chunks

        # Initialize Speech Recognizer
        self.recognizer = sr.Recognizer()
        self.microphone = sr.Microphone()

        # Adjust for ambient noise
        print("Adjusting for ambient noise... Please wait.")
        with self.microphone as source:
            self.recognizer.adjust_for_ambient_noise(source)
        print("Ready to listen!")

        self.is_recording = False
        
    def record_audio_chunk(self):
        """Record a chunk of audio using microphone"""
        try:
            print("Listening...")
            with self.microphone as source:
                # Listen for audio with timeout
                audio_data = self.recognizer.listen(source, timeout=self.RECORD_SECONDS, phrase_time_limit=self.RECORD_SECONDS)

            return audio_data

        except sr.WaitTimeoutError:
            # No audio detected in the timeout period
            return None
        except Exception as e:
            print(f"Error recording audio: {e}")
            return None
    
    def audio_to_text(self, audio_data):
        """Convert audio to text using speech recognition"""
        try:
            # Try Hindi first, then English
            try:
                text = self.recognizer.recognize_google(audio_data, language='hi-IN')
                return text
            except:
                text = self.recognizer.recognize_google(audio_data, language='en-US')
                return text
        except sr.UnknownValueError:
            return None
        except sr.RequestError as e:
            print(f"Speech recognition error: {e}")
            return None
        except Exception as e:
            print(f"Error converting audio to text: {e}")
            return None
    
    def get_ai_response(self, text):
        """Get response from Gemini AI"""
        try:
            prompt = f"User said: {text}\nPlease respond in Hindi:"
            response = self.model.generate_content(prompt)
            return response.text
        except Exception as e:
            print(f"Error getting AI response: {e}")
            return "AI response error occurred"
    
    def start_listening(self):
        """Start continuous audio monitoring"""
        self.is_recording = True
        print("System Audio AI started. Press Ctrl+C to stop.")
        print("Speak into your microphone...")

        try:
            while self.is_recording:
                # Record audio chunk
                audio_data = self.record_audio_chunk()

                if audio_data:
                    # Convert to text
                    text = self.audio_to_text(audio_data)

                    if text:
                        print(f"\n[{datetime.now().strftime('%H:%M:%S')}] Detected: {text}")

                        # Get AI response
                        ai_response = self.get_ai_response(text)
                        print(f"[AI Response]: {ai_response}")
                        print("-" * 50)

                # Small delay before next recording
                time.sleep(0.1)

        except KeyboardInterrupt:
            print("\nStopping System Audio AI...")
            self.stop_listening()
    
    def stop_listening(self):
        """Stop audio monitoring"""
        self.is_recording = False
        print("System Audio AI stopped.")

def main():
    # Your Google Gemini API key
    API_KEY = "AIzaSyCJqF9zkb6U2vr5dQ7-JwHhBNp5TRDZjVY"
    
    # Create and start the system audio AI
    audio_ai = SystemAudioAI(API_KEY)
    audio_ai.start_listening()

if __name__ == "__main__":
    main()
