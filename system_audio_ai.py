import speech_recognition as sr
import google.generativeai as genai
import threading
import time
import os
import subprocess
import tempfile
import wave
import numpy as np
from datetime import datetime
try:
    import sounddevice as sd
    SOUNDDEVICE_AVAILABLE = True
except ImportError:
    SOUNDDEVICE_AVAILABLE = False
    print("sounddevice not available, will try alternative methods")

class SystemAudioAI:
    def __init__(self, api_key):
        # Configure Gemini AI
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-1.5-flash')

        # Audio settings for system audio capture
        self.CHANNELS = 2  # Stereo for system audio
        self.RATE = 44100
        self.RECORD_SECONDS = 5  # Record in 5-second chunks

        self.recognizer = sr.Recognizer()
        self.is_recording = False

        # Try to find the best method for system audio capture
        self.setup_audio_capture()
        
    def setup_audio_capture(self):
        """Setup audio capture method without requiring Stereo Mix"""
        print("Setting up system audio capture...")

        if SOUNDDEVICE_AVAILABLE:
            try:
                # Try to find WASAPI loopback device
                devices = sd.query_devices()
                for i, device in enumerate(devices):
                    device_name = device['name'].lower()
                    # Look for WASAPI loopback devices
                    if 'wasapi' in str(device).lower() and 'loopback' in str(device).lower():
                        self.capture_method = 'sounddevice_wasapi'
                        self.device_index = i
                        print(f"Found WASAPI loopback device: {device['name']}")
                        return

                # If no WASAPI loopback found, use default output device with loopback
                default_output = sd.query_devices(kind='output')
                if default_output:
                    self.capture_method = 'sounddevice_default'
                    self.device_index = default_output['index']
                    print(f"Using default output device for loopback: {default_output['name']}")
                    return

            except Exception as e:
                print(f"SoundDevice setup failed: {e}")

        # Fallback to FFmpeg method
        self.capture_method = 'ffmpeg'
        print("Using FFmpeg for system audio capture")
        print("This method captures system audio without requiring Stereo Mix")

    def record_audio_chunk_sounddevice(self):
        """Record system audio using sounddevice with WASAPI loopback"""
        try:
            print("Capturing system audio...")

            # Record audio using sounddevice
            audio_data = sd.rec(
                int(self.RECORD_SECONDS * self.RATE),
                samplerate=self.RATE,
                channels=self.CHANNELS,
                device=self.device_index,
                dtype=np.int16
            )
            sd.wait()  # Wait until recording is finished

            # Save to temporary WAV file
            temp_filename = f"temp_system_audio_{int(time.time())}.wav"

            # Convert numpy array to WAV file
            import scipy.io.wavfile as wavfile
            wavfile.write(temp_filename, self.RATE, audio_data)

            return temp_filename

        except Exception as e:
            print(f"Error recording with sounddevice: {e}")
            return None

    def record_audio_chunk_ffmpeg(self):
        """Record system audio using FFmpeg (no Stereo Mix required)"""
        try:
            print("Capturing system audio with FFmpeg...")

            temp_filename = f"temp_system_audio_{int(time.time())}.wav"

            # FFmpeg command to capture system audio on Windows
            ffmpeg_cmd = [
                'ffmpeg',
                '-f', 'dshow',
                '-i', 'audio="virtual-audio-capturer"',  # Virtual audio capturer
                '-t', str(self.RECORD_SECONDS),
                '-acodec', 'pcm_s16le',
                '-ar', str(self.RATE),
                '-ac', str(self.CHANNELS),
                '-y',  # Overwrite output file
                temp_filename
            ]

            # Alternative command for WASAPI loopback
            wasapi_cmd = [
                'ffmpeg',
                '-f', 'wasapi',
                '-i', 'default',
                '-t', str(self.RECORD_SECONDS),
                '-acodec', 'pcm_s16le',
                '-ar', str(self.RATE),
                '-ac', str(self.CHANNELS),
                '-y',
                temp_filename
            ]

            # Try WASAPI first, then fallback to dshow
            try:
                result = subprocess.run(wasapi_cmd, capture_output=True, text=True, timeout=self.RECORD_SECONDS + 5)
                if result.returncode == 0 and os.path.exists(temp_filename):
                    return temp_filename
            except:
                pass

            # Fallback to dshow
            try:
                result = subprocess.run(ffmpeg_cmd, capture_output=True, text=True, timeout=self.RECORD_SECONDS + 5)
                if result.returncode == 0 and os.path.exists(temp_filename):
                    return temp_filename
            except:
                pass

            return None

        except Exception as e:
            print(f"Error recording with FFmpeg: {e}")
            return None

    def record_audio_chunk(self):
        """Record a chunk of system audio using the best available method"""
        if not self.is_recording:
            return None

        if hasattr(self, 'capture_method'):
            if self.capture_method == 'sounddevice_wasapi' or self.capture_method == 'sounddevice_default':
                return self.record_audio_chunk_sounddevice()
            elif self.capture_method == 'ffmpeg':
                return self.record_audio_chunk_ffmpeg()

        # Fallback
        return self.record_audio_chunk_ffmpeg()
    
    def audio_to_text(self, audio_file):
        """Convert audio file to text using speech recognition"""
        try:
            with sr.AudioFile(audio_file) as source:
                audio_data = self.recognizer.record(source)
                # Try English first for system audio (meetings, etc.)
                try:
                    text = self.recognizer.recognize_google(audio_data, language='en-IN')
                    return text
                except:
                    # Fallback to Hindi if English fails
                    text = self.recognizer.recognize_google(audio_data, language='en-US')
                    return text
        except sr.UnknownValueError:
            return None
        except sr.RequestError as e:
            print(f"Speech recognition error: {e}")
            return None
        except Exception as e:
            print(f"Error converting audio to text: {e}")
            return None
    
    def get_ai_response(self, text):
        """Get response from Gemini AI"""
        try:
            prompt = f"Someone said in a meeting/call: '{text}'\nPlease provide a brief, helpful response in English:"
            response = self.model.generate_content(prompt)
            return response.text
        except Exception as e:
            print(f"Error getting AI response: {e}")
            return "AI response error occurred"

    def cleanup_temp_file(self, filename):
        """Delete temporary audio file"""
        try:
            if os.path.exists(filename):
                os.remove(filename)
        except Exception as e:
            print(f"Error cleaning up file {filename}: {e}")
    
    def start_listening(self):
        """Start continuous system audio monitoring"""
        self.is_recording = True
        print("System Audio AI started. Press Ctrl+C to stop.")
        print("Monitoring system audio (speakers/meetings)...")
        print("Make sure 'Stereo Mix' is enabled in Windows sound settings if needed.")

        try:
            while self.is_recording:
                # Record system audio chunk
                audio_file = self.record_audio_chunk()

                if audio_file:
                    # Convert to text
                    text = self.audio_to_text(audio_file)

                    if text and len(text.strip()) > 0:
                        print(f"\n[{datetime.now().strftime('%H:%M:%S')}] System Audio Detected: {text}")

                        # Get AI response
                        ai_response = self.get_ai_response(text)
                        print(f"[AI Response]: {ai_response}")
                        print("-" * 70)

                    # Clean up temporary file
                    self.cleanup_temp_file(audio_file)

                # Small delay before next recording
                time.sleep(0.5)

        except KeyboardInterrupt:
            print("\nStopping System Audio AI...")
            self.stop_listening()
    
    def stop_listening(self):
        """Stop audio monitoring"""
        self.is_recording = False
        print("System Audio AI stopped.")

def main():
    # Your Google Gemini API key
    API_KEY = "AIzaSyCJqF9zkb6U2vr5dQ7-JwHhBNp5TRDZjVY"
    
    # Create and start the system audio AI
    audio_ai = SystemAudioAI(API_KEY)
    audio_ai.start_listening()

if __name__ == "__main__":
    main()
