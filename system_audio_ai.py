import speech_recognition as sr
import google.generativeai as genai
import threading
import time
import os
import subprocess
import tempfile
import pyaudio
import wave
import numpy as np
from datetime import datetime

class SystemAudioAI:
    def __init__(self, api_key):
        # Configure Gemini AI
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-1.5-flash')

        # Audio settings for system audio capture
        self.CHUNK = 1024
        self.FORMAT = pyaudio.paInt16
        self.CHANNELS = 2  # Stereo for system audio
        self.RATE = 44100
        self.RECORD_SECONDS = 5  # Record in 5-second chunks

        # Initialize PyAudio
        self.audio = pyaudio.PyAudio()
        self.recognizer = sr.Recognizer()

        # Find system audio device (speakers/output device used as input)
        self.find_system_audio_device()

        self.is_recording = False
        
    def find_system_audio_device(self):
        """Find system audio output device that can be used as input (loopback)"""
        print("Searching for system audio devices...")

        # List all audio devices
        for i in range(self.audio.get_device_count()):
            device_info = self.audio.get_device_info_by_index(i)
            device_name = device_info['name'].lower()

            # Look for stereo mix, loopback, or output devices that can be used as input
            if any(keyword in device_name for keyword in ['stereo mix', 'loopback', 'what u hear', 'wave out mix']):
                if device_info['maxInputChannels'] > 0:
                    self.device_index = i
                    print(f"Found system audio device: {device_info['name']}")
                    return

            # Also check for output devices that might support loopback
            if 'speakers' in device_name or 'output' in device_name:
                if device_info['maxInputChannels'] > 0:
                    self.device_index = i
                    print(f"Found potential system audio device: {device_info['name']}")
                    return

        # If no specific system audio device found, try to use default input
        print("No dedicated system audio device found. Using default input device.")
        print("Note: You may need to enable 'Stereo Mix' in Windows sound settings.")
        try:
            default_device = self.audio.get_default_input_device_info()
            self.device_index = default_device['index']
            print(f"Using: {default_device['name']}")
        except:
            self.device_index = None
            print("No audio input device available!")

    def record_audio_chunk(self):
        """Record a chunk of system audio"""
        if self.device_index is None:
            print("No audio device available!")
            return None

        try:
            print("Listening to system audio...")

            # Open audio stream for system audio capture
            stream = self.audio.open(
                format=self.FORMAT,
                channels=self.CHANNELS,
                rate=self.RATE,
                input=True,
                input_device_index=self.device_index,
                frames_per_buffer=self.CHUNK
            )

            frames = []
            for i in range(0, int(self.RATE / self.CHUNK * self.RECORD_SECONDS)):
                if not self.is_recording:
                    break
                try:
                    data = stream.read(self.CHUNK, exception_on_overflow=False)
                    frames.append(data)
                except Exception as e:
                    print(f"Audio read error: {e}")
                    break

            stream.stop_stream()
            stream.close()

            if not frames:
                return None

            # Save to temporary WAV file
            temp_filename = f"temp_system_audio_{int(time.time())}.wav"
            wf = wave.open(temp_filename, 'wb')
            wf.setnchannels(self.CHANNELS)
            wf.setsampwidth(self.audio.get_sample_size(self.FORMAT))
            wf.setframerate(self.RATE)
            wf.writeframes(b''.join(frames))
            wf.close()

            return temp_filename

        except Exception as e:
            print(f"Error recording system audio: {e}")
            return None
    
    def audio_to_text(self, audio_file):
        """Convert audio file to text using speech recognition"""
        try:
            with sr.AudioFile(audio_file) as source:
                audio_data = self.recognizer.record(source)
                # Try English first for system audio (meetings, etc.)
                try:
                    text = self.recognizer.recognize_google(audio_data, language='en-IN')
                    return text
                except:
                    # Fallback to Hindi if English fails
                    text = self.recognizer.recognize_google(audio_data, language='en-US')
                    return text
        except sr.UnknownValueError:
            return None
        except sr.RequestError as e:
            print(f"Speech recognition error: {e}")
            return None
        except Exception as e:
            print(f"Error converting audio to text: {e}")
            return None
    
    def get_ai_response(self, text):
        """Get response from Gemini AI"""
        try:
            prompt = f"Someone said in a meeting/call: '{text}'\nPlease provide a brief, helpful response in English:"
            response = self.model.generate_content(prompt)
            return response.text
        except Exception as e:
            print(f"Error getting AI response: {e}")
            return "AI response error occurred"

    def cleanup_temp_file(self, filename):
        """Delete temporary audio file"""
        try:
            if os.path.exists(filename):
                os.remove(filename)
        except Exception as e:
            print(f"Error cleaning up file {filename}: {e}")
    
    def start_listening(self):
        """Start continuous system audio monitoring"""
        self.is_recording = True
        print("System Audio AI started. Press Ctrl+C to stop.")
        print("Monitoring system audio (speakers/meetings)...")
        print("Make sure 'Stereo Mix' is enabled in Windows sound settings if needed.")

        try:
            while self.is_recording:
                # Record system audio chunk
                audio_file = self.record_audio_chunk()

                if audio_file:
                    # Convert to text
                    text = self.audio_to_text(audio_file)

                    if text and len(text.strip()) > 0:
                        print(f"\n[{datetime.now().strftime('%H:%M:%S')}] System Audio Detected: {text}")

                        # Get AI response
                        ai_response = self.get_ai_response(text)
                        print(f"[AI Response]: {ai_response}")
                        print("-" * 70)

                    # Clean up temporary file
                    self.cleanup_temp_file(audio_file)

                # Small delay before next recording
                time.sleep(0.5)

        except KeyboardInterrupt:
            print("\nStopping System Audio AI...")
            self.stop_listening()
    
    def stop_listening(self):
        """Stop audio monitoring"""
        self.is_recording = False
        self.audio.terminate()
        print("System Audio AI stopped.")

def main():
    # Your Google Gemini API key
    API_KEY = "AIzaSyCJqF9zkb6U2vr5dQ7-JwHhBNp5TRDZjVY"
    
    # Create and start the system audio AI
    audio_ai = SystemAudioAI(API_KEY)
    audio_ai.start_listening()

if __name__ == "__main__":
    main()
