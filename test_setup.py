#!/usr/bin/env python3
"""
Test script to check if all required packages are available
"""

def test_imports():
    """Test if all required packages can be imported"""
    print("Testing package imports...")
    
    try:
        import speech_recognition as sr
        print("✓ SpeechRecognition imported successfully")
    except ImportError as e:
        print(f"✗ SpeechRecognition import failed: {e}")
        return False
    
    try:
        import google.generativeai as genai
        print("✓ Google Generative AI imported successfully")
    except ImportError as e:
        print(f"✗ Google Generative AI import failed: {e}")
        return False
    
    # Test other standard library imports
    try:
        import threading
        import time
        import os
        import tempfile
        from datetime import datetime
        print("✓ Standard library imports successful")
    except ImportError as e:
        print(f"✗ Standard library import failed: {e}")
        return False
    
    return True

def test_microphone():
    """Test microphone functionality"""
    print("\nTesting microphone...")
    
    try:
        import speech_recognition as sr
        
        # Initialize recognizer and microphone
        recognizer = sr.Recognizer()
        
        # List available microphones
        mic_list = sr.Microphone.list_microphone_names()
        print(f"Available microphones: {len(mic_list)}")
        
        if not mic_list:
            print("✗ No microphones found")
            return False
        
        print("✓ Microphones found:")
        for i, name in enumerate(mic_list[:3]):  # Show first 3
            print(f"  {i}: {name}")
        
        # Test default microphone
        microphone = sr.Microphone()
        print("✓ Default microphone initialized")
        
        return True
        
    except Exception as e:
        print(f"✗ Microphone test failed: {e}")
        return False

def test_ai_connection():
    """Test AI API connection"""
    print("\nTesting AI connection...")
    
    try:
        import google.generativeai as genai
        
        # Configure with API key
        API_KEY = "AIzaSyCJqF9zkb6U2vr5dQ7-JwHhBNp5TRDZjVY"
        genai.configure(api_key=API_KEY)
        
        # Initialize model
        model = genai.GenerativeModel('gemini-1.5-flash')
        print("✓ Gemini model initialized")
        
        # Test a simple request
        response = model.generate_content("Hello, respond with 'AI connection successful'")
        print(f"✓ AI Response: {response.text}")
        
        return True
        
    except Exception as e:
        print(f"✗ AI connection test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("System Audio AI - Setup Test")
    print("=" * 40)
    
    all_passed = True
    
    # Test imports
    if not test_imports():
        all_passed = False
        print("\n❌ Package imports failed. Please install required packages:")
        print("pip install SpeechRecognition google-generativeai")
        return
    
    # Test microphone
    if not test_microphone():
        all_passed = False
        print("\n❌ Microphone test failed. Please check microphone permissions.")
    
    # Test AI connection
    if not test_ai_connection():
        all_passed = False
        print("\n❌ AI connection failed. Please check internet connection and API key.")
    
    if all_passed:
        print("\n🎉 All tests passed! You can now run:")
        print("python system_audio_ai.py")
    else:
        print("\n❌ Some tests failed. Please fix the issues above.")

if __name__ == "__main__":
    main()
