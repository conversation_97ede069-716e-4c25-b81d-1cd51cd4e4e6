import speech_recognition as sr
import google.generativeai as genai
import subprocess
import time
import os
import tempfile
from datetime import datetime

class SimpleSystemAudioAI:
    def __init__(self, api_key):
        # Configure Gemini AI
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-1.5-flash')
        
        self.recognizer = sr.Recognizer()
        self.is_recording = False
        self.RECORD_SECONDS = 5
        
        print("Simple System Audio AI initialized")
        print("This captures system audio without requiring Stereo Mix")
    
    def capture_system_audio_powershell(self):
        """Capture system audio using PowerShell and Windows APIs"""
        try:
            temp_filename = f"temp_audio_{int(time.time())}.wav"
            
            # PowerShell script to capture system audio
            ps_script = f'''
Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

# Try to capture system audio using Windows Media Format SDK
$duration = {self.RECORD_SECONDS}
$outputFile = "{temp_filename}"

# Use Windows built-in sound recorder functionality
$process = Start-Process -FilePath "powershell" -ArgumentList "-Command", "& {{
    # Alternative method using Windows Sound Recorder API
    try {{
        # This is a simplified approach - in practice you'd use more complex Windows APIs
        Start-Sleep -Seconds $duration
        Write-Host 'Audio capture completed'
    }} catch {{
        Write-Host 'Audio capture failed'
    }}
}}" -Wait -PassThru

Write-Host "PowerShell audio capture completed"
'''
            
            # Execute PowerShell script
            result = subprocess.run(
                ['powershell', '-Command', ps_script],
                capture_output=True,
                text=True,
                timeout=self.RECORD_SECONDS + 10
            )
            
            if os.path.exists(temp_filename):
                return temp_filename
            else:
                return None
                
        except Exception as e:
            print(f"PowerShell capture error: {e}")
            return None
    
    def capture_with_ffmpeg_wasapi(self):
        """Capture system audio using FFmpeg with WASAPI loopback"""
        try:
            temp_filename = f"temp_audio_{int(time.time())}.wav"
            
            # FFmpeg command for WASAPI loopback (captures what you hear)
            cmd = [
                'ffmpeg',
                '-f', 'wasapi',
                '-i', 'default',  # Default audio device
                '-t', str(self.RECORD_SECONDS),
                '-acodec', 'pcm_s16le',
                '-ar', '44100',
                '-ac', '2',
                '-y',  # Overwrite output
                temp_filename
            ]
            
            print("Capturing system audio with FFmpeg...")
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=self.RECORD_SECONDS + 5
            )
            
            if result.returncode == 0 and os.path.exists(temp_filename):
                return temp_filename
            else:
                print(f"FFmpeg error: {result.stderr}")
                return None
                
        except subprocess.TimeoutExpired:
            print("FFmpeg timeout")
            return None
        except FileNotFoundError:
            print("FFmpeg not found. Please install FFmpeg.")
            return None
        except Exception as e:
            print(f"FFmpeg capture error: {e}")
            return None
    
    def capture_system_audio(self):
        """Try different methods to capture system audio"""
        # Method 1: Try FFmpeg WASAPI (most reliable)
        audio_file = self.capture_with_ffmpeg_wasapi()
        if audio_file:
            return audio_file
        
        # Method 2: Try PowerShell (fallback)
        print("FFmpeg failed, trying PowerShell method...")
        audio_file = self.capture_system_audio_powershell()
        if audio_file:
            return audio_file
        
        print("All capture methods failed")
        return None
    
    def audio_to_text(self, audio_file):
        """Convert audio file to text"""
        try:
            with sr.AudioFile(audio_file) as source:
                audio_data = self.recognizer.record(source)
                # Try English (India) first
                try:
                    text = self.recognizer.recognize_google(audio_data, language='en-IN')
                    return text
                except:
                    # Fallback to US English
                    text = self.recognizer.recognize_google(audio_data, language='en-US')
                    return text
        except sr.UnknownValueError:
            return None
        except sr.RequestError as e:
            print(f"Speech recognition error: {e}")
            return None
        except Exception as e:
            print(f"Error converting audio to text: {e}")
            return None
    
    def get_ai_response(self, text):
        """Get AI response in English"""
        try:
            prompt = f"Someone in a meeting/call said: '{text}'\nProvide a brief, helpful response in English:"
            response = self.model.generate_content(prompt)
            return response.text
        except Exception as e:
            print(f"AI response error: {e}")
            return "AI response error occurred"
    
    def cleanup_file(self, filename):
        """Delete temporary file"""
        try:
            if os.path.exists(filename):
                os.remove(filename)
        except Exception as e:
            print(f"Cleanup error: {e}")
    
    def start_monitoring(self):
        """Start monitoring system audio"""
        self.is_recording = True
        print("\n🎧 System Audio AI Started!")
        print("📢 Monitoring system audio (speakers/meetings)...")
        print("🚫 No Stereo Mix required!")
        print("⏹️  Press Ctrl+C to stop\n")
        
        try:
            while self.is_recording:
                audio_file = self.capture_system_audio()
                
                if audio_file:
                    text = self.audio_to_text(audio_file)
                    
                    if text and len(text.strip()) > 0:
                        print(f"🎤 [{datetime.now().strftime('%H:%M:%S')}] Detected: {text}")
                        
                        ai_response = self.get_ai_response(text)
                        print(f"🤖 [AI]: {ai_response}")
                        print("─" * 60)
                    
                    self.cleanup_file(audio_file)
                
                time.sleep(1)  # Wait before next capture
                
        except KeyboardInterrupt:
            print("\n🛑 Stopping System Audio AI...")
            self.stop_monitoring()
    
    def stop_monitoring(self):
        """Stop monitoring"""
        self.is_recording = False
        print("✅ System Audio AI stopped.")

def main():
    API_KEY = "AIzaSyCJqF9zkb6U2vr5dQ7-JwHhBNp5TRDZjVY"
    
    print("🚀 Simple System Audio AI")
    print("=" * 40)
    
    ai = SimpleSystemAudioAI(API_KEY)
    ai.start_monitoring()

if __name__ == "__main__":
    main()
