import speech_recognition as sr
import google.generativeai as genai
import subprocess
import time
import os
import tempfile
import json
from datetime import datetime
import ctypes
from ctypes import wintypes, windll

class WindowsSystemAudioAI:
    def __init__(self, api_key):
        # Configure Gemini AI
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-1.5-flash')
        
        self.recognizer = sr.Recognizer()
        self.is_recording = False
        self.RECORD_SECONDS = 5
        
        print("Windows System Audio AI - No External Tools Required!")
    
    def capture_with_windows_media_recorder(self):
        """Use Windows built-in media recorder via PowerShell"""
        try:
            temp_filename = f"system_audio_{int(time.time())}.wav"
            
            # PowerShell command to use Windows Media Foundation
            ps_command = f'''
$duration = {self.RECORD_SECONDS}
$outputFile = "{os.path.abspath(temp_filename)}"

# Use Windows Media Foundation to capture audio
Add-Type -TypeDefinition @"
using System;
using System.Runtime.InteropServices;

public class AudioCapture {{
    [DllImport("winmm.dll")]
    public static extern int waveOutGetNumDevs();
    
    [DllImport("winmm.dll")]
    public static extern int waveInGetNumDevs();
}}
"@

try {{
    # Get number of audio devices
    $outDevs = [AudioCapture]::waveOutGetNumDevs()
    $inDevs = [AudioCapture]::waveInGetNumDevs()
    
    Write-Host "Output devices: $outDevs, Input devices: $inDevs"
    
    # Use Windows Sound Recorder equivalent
    $recorder = New-Object -ComObject "WMPlayer.OCX"
    Start-Sleep -Seconds $duration
    
    Write-Host "Recording completed"
}} catch {{
    Write-Host "Error: $_"
}}
'''
            
            result = subprocess.run(
                ['powershell', '-Command', ps_command],
                capture_output=True,
                text=True,
                timeout=self.RECORD_SECONDS + 10
            )
            
            print(f"PowerShell output: {result.stdout}")
            if result.stderr:
                print(f"PowerShell error: {result.stderr}")
            
            return None  # This method needs more work
            
        except Exception as e:
            print(f"Windows Media Recorder error: {e}")
            return None
    
    def capture_with_windows_api(self):
        """Use Windows API directly to capture system audio"""
        try:
            # This is a simplified approach using Windows APIs
            temp_filename = f"system_audio_{int(time.time())}.wav"
            
            # Use Windows Command Line to capture audio
            # This uses the built-in Windows capabilities
            cmd_script = f'''
@echo off
echo Capturing system audio for {self.RECORD_SECONDS} seconds...
timeout /t {self.RECORD_SECONDS} /nobreak > nul
echo Audio capture completed
'''
            
            # Write batch script
            batch_file = "capture_audio.bat"
            with open(batch_file, 'w') as f:
                f.write(cmd_script)
            
            # Execute batch script
            result = subprocess.run([batch_file], capture_output=True, text=True)
            
            # Clean up batch file
            if os.path.exists(batch_file):
                os.remove(batch_file)
            
            return None  # This is a placeholder
            
        except Exception as e:
            print(f"Windows API capture error: {e}")
            return None
    
    def capture_with_wmic(self):
        """Use WMIC (Windows Management Instrumentation) to get audio info"""
        try:
            # Get audio device information
            cmd = ['wmic', 'sounddev', 'get', 'name,status', '/format:json']
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("Audio devices found via WMIC:")
                print(result.stdout)
            
            return None
            
        except Exception as e:
            print(f"WMIC error: {e}")
            return None
    
    def simulate_system_audio_capture(self):
        """Simulate system audio capture for demonstration"""
        try:
            print(f"🎧 Simulating system audio capture for {self.RECORD_SECONDS} seconds...")
            
            # In a real implementation, this would capture actual system audio
            # For now, we'll simulate it by waiting
            time.sleep(self.RECORD_SECONDS)
            
            # Return None to indicate no audio was captured
            # In practice, you would return the audio file path
            return None
            
        except Exception as e:
            print(f"Simulation error: {e}")
            return None
    
    def get_microphone_as_system_audio(self):
        """Use microphone to capture system audio (if speakers are playing)"""
        try:
            print("🎤 Using microphone to capture system audio...")
            print("📢 Make sure your speakers are playing and microphone can hear them")
            
            # Use speech recognition with microphone
            with sr.Microphone() as source:
                print("🔊 Adjusting for ambient noise...")
                self.recognizer.adjust_for_ambient_noise(source, duration=1)
                
                print(f"🎧 Listening for {self.RECORD_SECONDS} seconds...")
                try:
                    audio_data = self.recognizer.listen(source, timeout=self.RECORD_SECONDS, phrase_time_limit=self.RECORD_SECONDS)
                    return audio_data
                except sr.WaitTimeoutError:
                    return None
                    
        except Exception as e:
            print(f"Microphone capture error: {e}")
            return None
    
    def audio_to_text(self, audio_data):
        """Convert audio to text"""
        if audio_data is None:
            return None
            
        try:
            # Try English (India) first
            try:
                text = self.recognizer.recognize_google(audio_data, language='en-IN')
                return text
            except:
                # Fallback to US English
                text = self.recognizer.recognize_google(audio_data, language='en-US')
                return text
        except sr.UnknownValueError:
            return None
        except sr.RequestError as e:
            print(f"Speech recognition error: {e}")
            return None
        except Exception as e:
            print(f"Error converting audio to text: {e}")
            return None
    
    def get_ai_response(self, text):
        """Get AI response in English"""
        try:
            prompt = f"Someone in a meeting/call said: '{text}'\nProvide a brief, helpful response in English:"
            response = self.model.generate_content(prompt)
            return response.text
        except Exception as e:
            print(f"AI response error: {e}")
            return "AI response error occurred"
    
    def start_monitoring(self):
        """Start monitoring system audio"""
        self.is_recording = True
        
        print("\n" + "="*60)
        print("🚀 WINDOWS SYSTEM AUDIO AI STARTED!")
        print("="*60)
        print("📋 IMPORTANT NOTES:")
        print("   • No external tools required")
        print("   • Uses Windows built-in capabilities")
        print("   • Currently using microphone as system audio input")
        print("   • Make sure speakers are audible to microphone")
        print("   • For true system audio capture, Windows needs")
        print("     advanced programming with Windows Audio APIs")
        print("="*60)
        print("🎧 Monitoring audio... Press Ctrl+C to stop")
        print("="*60)
        
        try:
            while self.is_recording:
                # Try different capture methods
                audio_data = self.get_microphone_as_system_audio()
                
                if audio_data:
                    text = self.audio_to_text(audio_data)
                    
                    if text and len(text.strip()) > 0:
                        print(f"\n🎤 [{datetime.now().strftime('%H:%M:%S')}] Detected: {text}")
                        
                        ai_response = self.get_ai_response(text)
                        print(f"🤖 [AI Response]: {ai_response}")
                        print("─" * 60)
                else:
                    print(".", end="", flush=True)  # Show activity
                
                time.sleep(0.5)
                
        except KeyboardInterrupt:
            print("\n🛑 Stopping Windows System Audio AI...")
            self.stop_monitoring()
    
    def stop_monitoring(self):
        """Stop monitoring"""
        self.is_recording = False
        print("✅ Windows System Audio AI stopped.")
        print("\n📝 NOTE: For true system audio capture without microphone,")
        print("   you would need to implement Windows WASAPI or")
        print("   Windows Core Audio APIs in C++ and call from Python.")

def check_windows_audio_capabilities():
    """Check what audio capabilities are available on Windows"""
    print("🔍 Checking Windows Audio Capabilities...")
    print("-" * 50)
    
    try:
        # Check available microphones
        mic_list = sr.Microphone.list_microphone_names()
        print(f"📱 Available microphones: {len(mic_list)}")
        for i, name in enumerate(mic_list[:3]):
            print(f"   {i}: {name}")
        
        # Check Windows audio services
        services_cmd = ['sc', 'query', 'AudioSrv']
        result = subprocess.run(services_cmd, capture_output=True, text=True)
        if 'RUNNING' in result.stdout:
            print("✅ Windows Audio Service is running")
        else:
            print("❌ Windows Audio Service issue")
        
        print("-" * 50)
        
    except Exception as e:
        print(f"Error checking capabilities: {e}")

def main():
    API_KEY = "AIzaSyCJqF9zkb6U2vr5dQ7-JwHhBNp5TRDZjVY"
    
    print("🪟 WINDOWS SYSTEM AUDIO AI")
    print("🚫 NO EXTERNAL TOOLS REQUIRED")
    print("=" * 50)
    
    # Check capabilities first
    check_windows_audio_capabilities()
    
    # Start the AI
    ai = WindowsSystemAudioAI(API_KEY)
    ai.start_monitoring()

if __name__ == "__main__":
    main()
