# System Audio AI

यह Python script आपके system audio को capture करके Google Gemini AI को भेजती है और response terminal में print करती है।

## Features

- ✅ Default audio device का automatic detection
- ✅ Bluetooth devices के साथ compatible
- ✅ बिना external application या stereo mix के काम करता है
- ✅ Real-time audio processing
- ✅ Hindi language support
- ✅ Google Gemini AI integration

## Requirements

- Python 3.7+
- Microphone या audio input device
- Internet connection (for AI API calls)

## Installation

1. **Setup चलाएं:**
   ```bash
   python setup.py
   ```

2. **Manual installation (अगर setup.py काम न करे):**
   ```bash
   pip install pyaudio SpeechRecognition google-generativeai
   ```

## Usage

1. **Script चलाएं:**
   ```bash
   python system_audio_ai.py
   ```

2. **बंद करने के लिए:** `Ctrl+C` दबाएं

## How it works

1. **Audio Capture:** Script आपके default audio device से 5-second chunks में audio record करती है
2. **Speech Recognition:** Audio को text में convert करती है (Google Speech Recognition API)
3. **AI Processing:** Text को Google Gemini AI को भेजती है
4. **Response:** AI का response terminal में print होता है

## Configuration

### Audio Settings
- **CHUNK:** 1024 (audio buffer size)
- **FORMAT:** 16-bit PCM
- **CHANNELS:** 2 (stereo) या 1 (mono) - device के अनुसार automatic
- **RATE:** 44100 Hz
- **RECORD_SECONDS:** 5 seconds per chunk

### API Key
Script में आपकी Google Gemini API key already configured है।

## Troubleshooting

### PyAudio Installation Issues
Windows पर अगर PyAudio install नहीं हो रहा:
```bash
pip install pipwin
pipwin install pyaudio
```

### Audio Device Issues
- Script automatically default device detect करती है
- अगर कोई device नहीं मिला तो error message दिखेगा
- `setup.py` चलाकर available devices देख सकते हैं

### Permission Issues
- Microphone access की permission check करें
- Windows में Privacy Settings > Microphone में app को allow करें

## File Structure

```
SYSTEM SOUND/
├── system_audio_ai.py    # Main script
├── setup.py             # Setup और device checking
├── requirements.txt     # Required packages
└── README.md           # यह file
```

## Notes

- Script continuous mode में चलती है
- हर 5 seconds में audio capture होता है
- Temporary audio files automatically delete हो जाती हैं
- Internet connection जरूरी है AI API calls के लिए
- Hindi और English दोनों languages support करती है
