# Pure Windows System Audio AI

🪟 **Zero External Dependencies** - सिर्फ Windows built-in capabilities का उपयोग करके system audio को capture करके Google Gemini AI को भेजती है।

## 🎯 Features

- ✅ **No External Tools Required** - कोई FFmpeg, PyAudio, या अन्य tools की जरूरत नहीं
- ✅ **No Stereo Mix Required** - Windows के built-in audio APIs का उपयोग
- ✅ **System Audio Capture** - Meetings, YouTube, music से audio capture
- ✅ **English (India) Support** - AI responses English में
- ✅ **Real-time Processing** - Live audio को text में convert करके AI response
- ✅ **Smart Microphone Mode** - System audio को microphone के through capture

## 📋 Requirements

- Windows 10/11
- Python 3.7+ (with built-in libraries only)
- Internet connection (for AI API calls)
- Working microphone/audio input device

## 🚀 Quick Start

**सबसे आसान तरीका:**
```bash
python pure_windows_audio_ai.py
```

## 📁 Available Scripts

1. **`pure_windows_audio_ai.py`** - 🌟 **RECOMMENDED** - Zero dependencies
2. **`windows_system_audio_ai.py`** - Windows-specific implementation
3. **`system_audio_ai.py`** - Advanced version (requires additional packages)

## 🎧 How It Works

1. **Audio Capture:** Windows built-in audio APIs से system audio capture
2. **Smart Detection:** Microphone को system audio के लिए optimize करता है
3. **Speech Recognition:** Google Speech Recognition API (built-in)
4. **AI Processing:** Google Gemini AI को text भेजता है
5. **Response:** English में AI response terminal में print होता है

## 💡 Usage Tips

### For Online Meetings:
1. **Join meeting** with speakers ON
2. **Run the script:** `python pure_windows_audio_ai.py`
3. **Position microphone** near speakers
4. **AI will respond** to what others say in the meeting

### For Better System Audio Capture:
1. **Enable "Listen to this device"** in microphone properties:
   - Right-click speaker icon → Sounds → Recording tab
   - Right-click microphone → Properties → Listen tab
   - Check "Listen to this device"
2. **Increase speaker volume**
3. **Use headphones with microphone** for meetings

## 🔧 Configuration

- **Language:** English (India) primary, English (US) fallback
- **Audio Duration:** 5 seconds per capture
- **AI Model:** Google Gemini 1.5 Flash
- **Response Format:** Brief, helpful English responses

## 🛠️ Troubleshooting

### No Audio Detected:
- Check microphone permissions in Windows Privacy Settings
- Increase speaker volume
- Test with YouTube/music playing
- Ensure microphone is not muted

### AI Response Issues:
- Check internet connection
- Verify Google Gemini API key is working
- Try speaking more clearly

### Windows Audio Issues:
- Restart Windows Audio Service: `sc stop AudioSrv && sc start AudioSrv`
- Check if microphone is working in other apps
- Update audio drivers

## 📁 File Structure

```
SYSTEM SOUND/
├── pure_windows_audio_ai.py     # 🌟 Main script (RECOMMENDED)
├── windows_system_audio_ai.py   # Windows-specific version
├── system_audio_ai.py           # Advanced version
├── test_setup.py               # Setup testing
├── requirements.txt            # Package list
└── README.md                   # This file
```

## 🎯 Use Cases

- **Online Meetings:** Get AI responses to meeting discussions
- **Language Learning:** AI responses to foreign language content
- **Content Analysis:** AI analysis of videos/podcasts
- **Accessibility:** Text responses for audio content
- **Meeting Notes:** AI summaries of important points

## ⚠️ Important Notes

- **Privacy:** Audio is processed by Google APIs
- **Internet Required:** For speech recognition and AI responses
- **Windows Only:** Uses Windows-specific audio capabilities
- **Microphone Based:** Currently uses microphone to capture system audio
- **No Recording:** Audio is processed in real-time, not stored
